import React, { useState, useEffect } from 'react';
import {
    Card,
    Checkbox,
    Select,
    Row,
    Col,
    Button,
    message,
    Spin,
    Alert,
    Tag,
    Radio,
} from 'antd';
import moment from 'moment';
import http_utils from '../../../util/http_utils';
import './booking.css';
import FormBuilder from 'antd-form-builder';
import { render } from '@testing-library/react';
import TimePickerWidget from '../../../components/wify-utils/TimePickerWidget';
import ConfigHelpers from '../../../util/ConfigHelpers';

const { Option } = Select;

const MicroBookingSlot = ({
    savedSlots,
    disableSaveButton,
    allSrvcPrvdrs,
    allowedSrvcPrvdrs,
    formRef,
    initialValues,
    current_prvdr,
    editMode,
    default_srvc_prvdr,
    srvcConfigData,
    isSpReassignmentAllowed,
    vertical_id,
    isCustAccess,
}) => {
    const forceUpdate = FormBuilder.useForceUpdate();
    const [selectedWeek, setSelectedWeek] = useState(null);
    const [weekOptions, setWeekOptions] = useState([]);
    const [weekData, setWeekData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [availableSlots, setAvailableSlots] = useState(undefined);
    const [selectedSlots, setSelectedSlots] = useState({});
    const [error, setError] = useState(null);
    const [capacityData, setCapacityData] = useState(null);
    const [selectedPrvdr, setSelectedPrvdr] = useState(null);
    console.log('initialValues', initialValues);
    useEffect(() => {
        generateWeekOptions();
    }, [selectedPrvdr]);

    useEffect(() => {
        if (savedSlots) {
            setSelectedSlots(savedSlots);
            fetchCapacityData(current_prvdr);
        }
    }, [savedSlots, selectedPrvdr]);

    // Fetch capacity data based on logged-in service provider
    useEffect(() => {
        // Check if the user is a service provider using ConfigHelpers
        if (ConfigHelpers.isServiceProvider()) {
            // If true, fetch capacity data for that provider
            fetchCapacityData(current_prvdr); // Assuming current_prvdr is the org_id
        }
    }, [current_prvdr]);

    // Monitor changes to `cust_pincode` using `useEffect`
    useEffect(() => {
        console.log('Pincode changed');
        // Fetch available slots whenever the pincode changes
        const pincode = formRef?.getFieldValue('cust_pincode');
        if (
            pincode &&
            (selectedPrvdr || ConfigHelpers.isServiceProvider()) &&
            selectedWeek
        ) {
            fetchAvailableSlots();
        } else if (
            !pincode &&
            (selectedPrvdr || ConfigHelpers.isServiceProvider()) &&
            selectedWeek
        ) {
            setAvailableSlots('pincode_missing');
        }
    }, [formRef?.getFieldValue('cust_pincode')]);

    useEffect(() => {
        if (default_srvc_prvdr && !formRef?.getFieldValue('new_prvdr')) {
            if (formRef) {
                formRef.setFieldsValue({
                    new_prvdr: default_srvc_prvdr,
                });
            }
            handlePrvdrSelector(default_srvc_prvdr);
        }
    }, [formRef]);

    const generateWeekOptions = () => {
        const weeks = [];
        const today = moment();

        // First week: today to upcoming Saturday
        const firstWeekEnd = today.clone().day(6); // 6 = Saturday
        if (firstWeekEnd.isBefore(today)) {
            firstWeekEnd.add(1, 'weeks'); // if today is Sunday, get next Saturday
        }

        weeks.push({
            value: `${today.format('YYYY-MM-DD')}_${firstWeekEnd.format('YYYY-MM-DD')}`,
            label: `(${today.format('ddd')}) ${today.format('Do MMM')} - (${firstWeekEnd.format('ddd')}) ${firstWeekEnd.format('Do MMM ')}`,
            startDate: today.format('YYYY-MM-DD'),
            endDate: firstWeekEnd.format('YYYY-MM-DD'),
        });

        // Generate next 4 weeks (Sun–Sat)
        const nextWeekStart = firstWeekEnd.clone().add(1, 'days'); // Sunday after first Saturday

        for (let i = 0; i < 4; i++) {
            const weekStart = nextWeekStart.clone().add(i * 7, 'days');
            const weekEnd = weekStart.clone().add(6, 'days');

            weeks.push({
                value: `${weekStart.format('YYYY-MM-DD')}_${weekEnd.format('YYYY-MM-DD')}`,
                label: `(${weekStart.format('ddd')}) ${weekStart.format('Do MMM')} - (${weekEnd.format('ddd')}) ${weekEnd.format('Do MMM ')}`,
                startDate: weekStart.format('YYYY-MM-DD'),
                endDate: weekEnd.format('YYYY-MM-DD'),
            });
        }

        setWeekOptions(weeks);
    };

    const refreshPage = () => {
        forceUpdate();
    };

    // Fetch org settings to get generated slots
    const fetchAvailableSlots = () => {
        if (loading) return;

        setLoading(true);
        setAvailableSlots(undefined);
        setError(undefined);
        let params = {
            org_id: selectedPrvdr,
            vertical_id: vertical_id,
            pincode: formRef?.getFieldValue('cust_pincode'),
        };
        const onComplete = (resp) => {
            // console.log('Availability slots response:', resp);
            setAvailableSlots(resp.data);
            setLoading(false);
        };

        const onError = (error) => {
            //console.error('Error fetching availability slots:', error);
            setAvailableSlots(undefined);
            setError(http_utils.decodeErrorToMessage(error));
            setLoading(false);
        };

        http_utils.performGetCall(
            '/services/availability-slots',
            params,
            onComplete,
            onError
        );
    };

    const fetchCapacityData = (prvdrId) => {
        if (loading) return;
        setLoading(true);
        setCapacityData(undefined);
        setError(undefined);
        let params = {
            srvc_prvdr_id: prvdrId,
        };

        const onComplete = (resp) => {
            // console.log('Availability slots response:', resp);
            setCapacityData(resp.data);
            setLoading(false);
        };

        const onError = (error) => {
            // console.error('Error fetching availability slots:', error);
            setCapacityData(undefined);
            setError(http_utils.decodeErrorToMessage(error));
            setLoading(false);
        };

        http_utils.performGetCall(
            '/services/capacity',
            params,
            onComplete,
            onError
        );
    };

    // Fetch week data when week is selected
    const fetchWeekData = async (weekValue) => {
        const selectedWeekOption = weekOptions.find(
            (w) => w.value === weekValue
        );
        if (!selectedWeekOption) return;

        try {
            setLoading(true);

            // Generate days for the selected week
            const startDate = moment(selectedWeekOption.startDate);
            const endDate = moment(selectedWeekOption.endDate);
            const days = [];

            let currentDate = startDate.clone();
            while (currentDate.isSameOrBefore(endDate)) {
                days.push({
                    date: currentDate.format('YYYY-MM-DD'),
                    dayName: currentDate.format('dddd'),
                    displayDate: currentDate.format('Do MMM'),
                });
                currentDate.add(1, 'day');
            }

            setWeekData({
                ...selectedWeekOption,
                days: days,
            });

            // Initialize selected slots for each day
            const initialSlots = {};
            days.forEach((day) => {
                initialSlots[day.date] = [];
            });
            setSelectedSlots(initialSlots);
        } catch (error) {
            console.error('Error fetching week data:', error);
            message.error('Failed to fetch week data');
        } finally {
            setLoading(false);
        }
    };

    // Handle week selection
    const handleWeekSelect = (value) => {
        fetchAvailableSlots();
        setSelectedWeek(value);
        fetchWeekData(value);
    };

    // Handle slot selection for a specific day
    const handleSlotSelection = (date, slotValue) => {
        setSelectedSlots((prev) => {
            const daySlots = prev[date] || [];
            const isSelected = daySlots.includes(slotValue);

            const updatedSlots = {
                ...prev,
                [date]: isSelected
                    ? daySlots.filter((slot) => slot !== slotValue)
                    : [...daySlots, slotValue],
            };

            // Save logic immediately after update
            const totalSelectedSlots = Object.values(updatedSlots).reduce(
                (total, slots) => total + slots.length,
                0
            );

            formRef.setFieldsValue({ booked_slots: updatedSlots });

            refreshPage();

            return updatedSlots;
        });
    };

    const getGeneratedSlots = () => {
        // Check if availableSlots is valid and contains the 'slots' property
        if (availableSlots && Array.isArray(availableSlots.slots)) {
            return availableSlots.slots.map((slot) => ({
                ...slot,
                value: slot.label,
            }));
        }

        // If availableSlots is not valid or 'slots' does not exist, return an empty array or default data
        return [];
    };

    const handlePrvdrSelector = (value) => {
        setSelectedPrvdr(value);
        fetchCapacityData(value);
        //if user changes prvdr, reset selected slots in creation mode
        setSelectedSlots({});
    };

    const getMeta = () => {
        const isPrvdr = ConfigHelpers.isServiceProvider();
        const isSrvcPrvdrSelected = formRef?.getFieldValue('new_prvdr');
        const isCapcityEnabledFrSelectedPrvdr =
            capacityData?.enable_capacity_module;
        const prvdrNotEnabledCapacity =
            (isSrvcPrvdrSelected || isPrvdr) &&
            !isCapcityEnabledFrSelectedPrvdr;
        const isSlotsBooked = Object.values(selectedSlots).reduce(
            (total, daySlots) => total + daySlots.length,
            0
        );
        const startOfDay =
            srvcConfigData?.sbtsk_time_slot_lower_limit || '05:00AM';
        const endOfDay =
            srvcConfigData?.sbtsk_time_slot_upper_limit || '11:45PM';
        const startTimeFrEndTime = formRef?.getFieldValue('start_time')
            ? formRef?.getFieldValue('start_time')
            : initialValues?.start_time;

        // Check if pincode is missing
        const isPincodeMissing = availableSlots === 'pincode_missing';

        return {
            columns: 4,
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                ...(!isPrvdr
                    ? [
                          {
                              key: 'new_prvdr',
                              label: 'Select Service Provider',
                              widget: 'select',
                              colSpan: 2,
                              options: allSrvcPrvdrs,
                              onChange: (value) => {
                                  handlePrvdrSelector(value);
                                  refreshPage();
                              },
                          },
                      ]
                    : []),
                ...((isSrvcPrvdrSelected || isPrvdr || selectedPrvdr) &&
                isCapcityEnabledFrSelectedPrvdr &&
                !isSlotsBooked
                    ? [
                          {
                              key: 'select_week',
                              label: 'Select Week',
                              widget: 'select',
                              colSpan: 2,
                              options: weekOptions,
                              onChange: (value) => {
                                  handleWeekSelect(value);
                                  refreshPage();
                              },
                          },
                      ]
                    : []),
                ...(prvdrNotEnabledCapacity
                    ? [
                          {
                              key: 'request_req_date',
                              label: 'Req. Service Date',
                              colSpan: 2,
                              widgetProps: {
                                  disabledDate: (current) =>
                                      current &&
                                      current > moment().add(2, 'month'),
                                  style: { width: '100%' },
                                  onChange: (value, dateString) => {
                                      formRef.setFieldsValue({
                                          request_req_date:
                                              moment.utc(dateString),
                                      });
                                  },
                              },
                              widget: 'date-picker',
                          },
                          {
                              key: 'start_time',
                              label: 'Start Time',
                              widget: TimePickerWidget,
                              widgetProps: {
                                  beginLimit: startOfDay,
                                  endLimit: endOfDay,
                                  step: 15,
                                  onChange: (value) => {
                                      refreshPage();
                                  },
                              },
                              colSpan: 2,
                          },
                          {
                              key: 'end_time',
                              label: 'End Time',
                              widget: TimePickerWidget,
                              widgetProps: {
                                  beginLimit: startTimeFrEndTime
                                      ? startTimeFrEndTime
                                      : startOfDay,
                                  endLimit: endOfDay,
                                  step: 15,
                              },
                              colSpan: 2,
                          },
                      ]
                    : []),
                {
                    key: 'booked_slots',
                    className: 'gx-d-none',
                    widgetProps: {
                        hidden: true,
                    },
                },
                ...((isSrvcPrvdrSelected || isPrvdr) &&
                isCapcityEnabledFrSelectedPrvdr &&
                !isPincodeMissing &&
                !areSlotsEmpty
                    ? [
                          {
                              key: 'booking_slots',
                              colSpan: 4,
                              render: () => {
                                  const hasSelectedSlots = Object.values(
                                      selectedSlots
                                  ).some((slots) => slots.length > 0);

                                  // ✅ Show only selected slot summary if slots are selected
                                  if (hasSelectedSlots) {
                                      return (
                                          <div className="selected-slot-summary">
                                              <div
                                                  style={{
                                                      fontSize: '14px',
                                                      fontWeight: 'bold',
                                                      marginBottom: '12px',
                                                      color: '#333',
                                                  }}
                                              >
                                                  SELECTED SLOT
                                              </div>

                                              {Object.entries(
                                                  selectedSlots
                                              ).map(([date, slots]) => {
                                                  if (slots.length === 0)
                                                      return null;

                                                  return (
                                                      <div
                                                          className="selected-slot-card gx-border-2 gx-border-green"
                                                          key={date}
                                                      >
                                                          <span className="selected-slot-day gx-d-flex gx-justify-content-between">
                                                              <div>
                                                                  {moment(
                                                                      date
                                                                  ).format(
                                                                      'dddd Do MMM'
                                                                  )}
                                                              </div>
                                                              {(isCustAccess ||
                                                                  !ConfigHelpers.isServiceProvider()) && (
                                                                  <div>
                                                                      <Button
                                                                          type="primary"
                                                                          size="small"
                                                                          onClick={() => {
                                                                              // Clear selected slots or toggle to full booking card
                                                                              setSelectedSlots(
                                                                                  {}
                                                                              );
                                                                              formRef.setFieldsValue(
                                                                                  {
                                                                                      booked_slots:
                                                                                          undefined,
                                                                                  }
                                                                              );
                                                                          }}
                                                                          style={{
                                                                              padding: 0,
                                                                          }}
                                                                      >
                                                                          Clear
                                                                      </Button>
                                                                  </div>
                                                              )}
                                                          </span>
                                                          <hr className="selected-slot-divider" />
                                                          <div className="selected-slot-tags">
                                                              {slots.map(
                                                                  (slot) => (
                                                                      <Tag
                                                                          key={`${date}-${slot}`}
                                                                          color="success"
                                                                          className="selected-slot-tag"
                                                                      >
                                                                          {slot}
                                                                      </Tag>
                                                                  )
                                                              )}
                                                          </div>
                                                      </div>
                                                  );
                                              })}
                                          </div>
                                      );
                                  }

                                  // 🟩 Else show the full slot booking card
                                  return (
                                      <>
                                          {weekData && (
                                              <Card className="booking-scroll-card">
                                                  <div className="booking-scroll-container">
                                                      {weekData?.days.map(
                                                          (day) => (
                                                              <div
                                                                  key={day.date}
                                                                  className="booking-day-card"
                                                              >
                                                                  <div className="booking-day-header">
                                                                      {
                                                                          day.dayName
                                                                      }{' '}
                                                                      <br />
                                                                      {
                                                                          day.displayDate
                                                                      }
                                                                  </div>

                                                                  <div className="slot-grid">
                                                                      {getGeneratedSlots()?.map(
                                                                          (
                                                                              slot
                                                                          ) => {
                                                                              const isSelected =
                                                                                  selectedSlots[
                                                                                      day
                                                                                          .date
                                                                                  ]?.includes(
                                                                                      slot.value
                                                                                  );

                                                                              return (
                                                                                  <div
                                                                                      key={`${day.date}-${slot.value}`}
                                                                                      onClick={() =>
                                                                                          handleSlotSelection(
                                                                                              day.date,
                                                                                              slot.value
                                                                                          )
                                                                                      }
                                                                                      className={`slot-box ${isSelected ? 'slot-box-selected' : ''}`}
                                                                                  >
                                                                                      <div>
                                                                                          {slot.label ||
                                                                                              slot.value}
                                                                                      </div>
                                                                                  </div>
                                                                              );
                                                                          }
                                                                      )}
                                                                  </div>
                                                              </div>
                                                          )
                                                      )}
                                                  </div>
                                              </Card>
                                          )}
                                      </>
                                  );
                              },
                          },
                      ]
                    : []),
            ],
        };
    };

    const areSlotsEmpty =
        availableSlots?.slots && availableSlots?.slots.length === 0;

    return (
        <div className="gx-mt-3">
            <FormBuilder
                meta={getMeta()}
                form={formRef}
                initialValues={initialValues}
            ></FormBuilder>
            {/* Show error below the selections if pincode is missing */}
            {availableSlots === 'pincode_missing' && (
                <div className="gx-text-red">
                    Please Enter Pincode to see availability slots.
                </div>
            )}
            {areSlotsEmpty && (
                <div className="gx-text-red">
                    No slots available for this pincode.
                </div>
            )}
        </div>
    );
};

export default MicroBookingSlot;
