CREATE OR REPLACE FUNCTION public.tms_ace_get_time_slot_by_vertical(form_data json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare 
	status boolean;
	message text;
	affected_rows integer;
	resp_data json;
	--form data
	org_id_ integer;
	usr_id_ uuid;
	ip_address_ text;
	user_agent_ text;
    day_ date;

	slots json;
	org_timezone text;
    service_hub int;
    pincode_ text;
    vertical_id_ int;
    -- usr_capacity_details json;

    org_availability_slots_config json;

begin

	status = false;
	message = 'Internal_error';
	--form data 
	usr_id_ = form_data->>'usr_id';
	ip_address_ = form_data->>'ip_address';  
	user_agent_ = form_data->>'user_agent';
	org_id_ = (form_data->>'org_id')::integer;
	day_ = DATE(form_data->>'date');
	pincode_ = form_data->>'pincode';	
    vertical_id_ = form_data->>'vertical_id';
	org_timezone = tms_hlpr_get_org_timezone(org_id_);

    if pincode_ is null then 
		status = false;
		message = 'pincode_missing';
		resp_data = jsonb_build_object(
		    'status', status,
		    'code', message
		);
		return resp_data;
   
    end if;

    select hub.id
	  from cl_tx_vertical_srvc_hubs as hub 
	 where pincode_ = any(hub.pincodes)
	   and hub.is_active = true
	   and vertical_id = vertical_id_
	  into service_hub;

	slots :=  array_to_json(array(
		select jsonb_build_object(
		    'db_id', slot_.db_id,
		    'start_time', to_char(
					    (
					       (('2000-01-01'::date + slot_.start_time) at time zone 'UTC')
					       at time zone org_timezone
					    ),
					    'hh12:miam'
					),
		    'end_time', to_char(
					    (
					       (('2000-01-01'::date + slot_.end_time) at time zone 'UTC')
					       at time zone org_timezone
					    ),
					    'hh12:miam'
					),
			 'label', 
				  to_char(
				    (('2000-01-01'::date + slot_.start_time) at time zone 'UTC') at time zone org_timezone,
				    'hh12:miam'
				  ) || ' - ' || 
				  to_char(
				    (('2000-01-01'::date + slot_.end_time) at time zone 'UTC') at time zone org_timezone,
				    'hh12:miam'
				  )

		  )
		from public.cl_tx_vertical_time_slots as slot_
	    left join public.cl_tx_vertical_time_slots_srvc_hubs as tsh 
		  on tsh.slot_db_id = slot_.db_id
		 and slot_.apply_to_all_hubs is not true 			
	   where slot_.vertical_id = vertical_id_
	     and (
               tsh.srvc_hub_id = service_hub
               OR slot_.apply_to_all_hubs is  true
           )
	   order 
		   by 
		      (
		         (
		            (('2000-01-01'::date + slot_.start_time) at time zone 'UTC')
		            at time zone org_timezone
		         )::time
		      ) asc
	));
    status = true;
    resp_data := jsonb_build_object(
        'status', status,
        'code', message,
        'data', jsonb_build_object('slots', slots)
    );

    return resp_data;

end;
$function$
;
